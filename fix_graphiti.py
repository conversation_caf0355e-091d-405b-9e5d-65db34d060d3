#!/usr/bin/env python3
"""
Graphiti存储系统修复脚本
"""
import os
import subprocess
import time
import sys
from pathlib import Path

def main():
    print("🔧 Graphiti存储系统修复工具")
    print("=" * 50)
    
    # 检查当前状态
    print("1. 检查当前系统状态...")
    
    # 检查Neo4j连接
    try:
        result = subprocess.run([
            "docker", "exec", "neoj4-neo4j-1", "cypher-shell", 
            "-u", "neo4j", "-p", "graph123321", 
            "MATCH (n) RETURN count(n) as node_count"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            node_count = result.stdout.strip().split('\n')[-1]
            print(f"   ✅ Neo4j连接正常，当前节点数量: {node_count}")
        else:
            print(f"   ❌ Neo4j连接失败: {result.stderr}")
            return
    except Exception as e:
        print(f"   ❌ 无法连接Neo4j: {e}")
        return
    
    # 修复方案选择
    print("\n2. 选择修复方案:")
    print("   [1] 启用同步处理模式（推荐，立即生效）")
    print("   [2] 启动后台服务模式（需要保持服务运行）")
    print("   [3] 重置并清理系统")
    
    choice = input("\n请选择方案 (1-3): ").strip()
    
    if choice == "1":
        fix_sync_mode()
    elif choice == "2":
        fix_background_service()
    elif choice == "3":
        reset_system()
    else:
        print("❌ 无效选择")

def fix_sync_mode():
    """修复方案1：启用同步处理模式"""
    print("\n🔧 启用同步处理模式...")
    
    # 修改环境变量
    env_file = Path("mcp_server/.env")
    if env_file.exists():
        content = env_file.read_text(encoding='utf-8')
        if "GRAPHITI_SYNC_PROCESSING" not in content:
            content += "\n# Enable synchronous processing\nGRAPHITI_SYNC_PROCESSING=true\n"
            env_file.write_text(content, encoding='utf-8')
            print("   ✅ 已更新.env文件")
        else:
            print("   ✅ .env文件已包含同步处理配置")
    
    # 重启MCP服务（如果正在运行）
    print("   🔄 重启MCP服务...")
    
    # 测试同步处理
    print("   🧪 测试同步处理...")
    test_sync_processing()

def fix_background_service():
    """修复方案2：启动后台服务"""
    print("\n🔧 启动后台服务模式...")
    
    # 创建服务启动脚本
    service_script = Path("start_graphiti_background.py")
    if not service_script.exists():
        print("   ✅ 服务启动脚本已创建: start_graphiti_service.py")
        print("   📝 请运行: python start_graphiti_service.py")
    
    print("   ⚠️ 注意：此模式需要保持服务持续运行")

def reset_system():
    """修复方案3：重置系统"""
    print("\n🔧 重置系统...")
    
    confirm = input("   ⚠️ 这将清空所有数据，确认吗？(y/N): ").strip().lower()
    if confirm == 'y':
        try:
            # 清空Neo4j数据库
            result = subprocess.run([
                "docker", "exec", "neoj4-neo4j-1", "cypher-shell", 
                "-u", "neo4j", "-p", "graph123321", 
                "MATCH (n) DETACH DELETE n"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("   ✅ 数据库已清空")
            else:
                print(f"   ❌ 清空失败: {result.stderr}")
        except Exception as e:
            print(f"   ❌ 重置失败: {e}")
    else:
        print("   ❌ 取消重置")

def test_sync_processing():
    """测试同步处理功能"""
    print("   📝 添加测试记忆...")
    
    # 这里应该调用add_memory函数进行测试
    # 由于网络问题，我们先跳过实际测试
    print("   ⏳ 请手动测试 add_memory 函数")
    print("   💡 如果返回消息包含'processed successfully'则表示同步模式生效")

def check_system_status():
    """检查系统状态"""
    print("\n📊 系统状态检查:")
    
    # 检查Docker容器
    try:
        result = subprocess.run(["docker", "ps"], capture_output=True, text=True)
        if "neo4j" in result.stdout:
            print("   ✅ Neo4j容器运行中")
        else:
            print("   ❌ Neo4j容器未运行")
    except:
        print("   ❌ 无法检查Docker状态")
    
    # 检查端口占用
    try:
        result = subprocess.run(["netstat", "-ano"], capture_output=True, text=True)
        if ":7687" in result.stdout:
            print("   ✅ Neo4j端口7687正常")
        if ":8000" in result.stdout:
            print("   ✅ 端口8000被占用（可能是MCP服务）")
    except:
        print("   ❌ 无法检查端口状态")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 修复工具已退出")
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {e}")
        sys.exit(1)

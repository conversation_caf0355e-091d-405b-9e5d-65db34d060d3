#!/usr/bin/env python3
"""
启动Graphiti MCP服务器作为后台服务
"""
import asyncio
import os
import sys
import signal
from pathlib import Path

# 设置环境变量
os.environ.setdefault("NEO4J_URI", "neo4j://127.0.0.1:7687")
os.environ.setdefault("NEO4J_USER", "neo4j")
os.environ.setdefault("NEO4J_PASSWORD", "graph123321")
os.environ.setdefault("OPENAI_API_KEY", "sk-9eb80fd95e324313970911c7ec8add64")
os.environ.setdefault("MODEL_NAME", "deepseek-chat")
os.environ.setdefault("OPENAI_BASE_URL", "https://api.deepseek.com/v1")

async def main():
    """启动MCP服务器"""
    print("🚀 启动Graphiti MCP服务器...")
    
    # 切换到mcp_server目录
    mcp_server_dir = Path(__file__).parent / "mcp_server"
    os.chdir(mcp_server_dir)
    
    # 启动MCP服务器进程
    process = await asyncio.create_subprocess_exec(
        "uv", "run", "graphiti_mcp_server.py", "--transport", "stdio",
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE
    )
    
    print(f"✅ MCP服务器已启动 (PID: {process.pid})")
    print("📝 服务器日志:")
    
    # 设置信号处理器
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在关闭服务器...")
        process.terminate()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 读取并显示输出
        while True:
            # 读取stdout
            if process.stdout:
                try:
                    line = await asyncio.wait_for(process.stdout.readline(), timeout=1.0)
                    if line:
                        print(f"📤 {line.decode().strip()}")
                except asyncio.TimeoutError:
                    pass
            
            # 读取stderr
            if process.stderr:
                try:
                    line = await asyncio.wait_for(process.stderr.readline(), timeout=1.0)
                    if line:
                        print(f"⚠️ {line.decode().strip()}")
                except asyncio.TimeoutError:
                    pass
            
            # 检查进程是否还在运行
            if process.returncode is not None:
                print(f"❌ MCP服务器已退出，返回码: {process.returncode}")
                break
                
            await asyncio.sleep(0.1)
    
    except KeyboardInterrupt:
        print("\n🛑 用户中断，正在关闭服务器...")
        process.terminate()
        await process.wait()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 服务器已关闭")

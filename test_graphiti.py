#!/usr/bin/env python3
"""
测试Graphiti存储系统的脚本
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加mcp_server目录到Python路径
mcp_server_path = Path(__file__).parent / "mcp_server"
sys.path.insert(0, str(mcp_server_path))

# 设置环境变量
os.environ.setdefault("NEO4J_URI", "neo4j://127.0.0.1:7687")
os.environ.setdefault("NEO4J_USER", "neo4j")
os.environ.setdefault("NEO4J_PASSWORD", "graph123321")
os.environ.setdefault("OPENAI_API_KEY", "sk-9eb80fd95e324313970911c7ec8add64")
os.environ.setdefault("MODEL_NAME", "deepseek-chat")
os.environ.setdefault("OPENAI_BASE_URL", "https://api.deepseek.com/v1")

async def test_graphiti():
    """测试Graphiti存储和检索功能"""
    try:
        # 导入必要的模块
        from graphiti_core import Graphiti
        from graphiti_core.nodes import EpisodeType
        
        print("🔧 初始化Graphiti客户端...")
        
        # 创建Graphiti客户端
        client = Graphiti(
            uri=os.environ["NEO4J_URI"],
            user=os.environ["NEO4J_USER"],
            password=os.environ["NEO4J_PASSWORD"],
        )
        
        # 配置LLM
        client.llm_client.config.api_key = os.environ["OPENAI_API_KEY"]
        client.llm_client.config.base_url = os.environ["OPENAI_BASE_URL"]
        client.llm_client.model = os.environ["MODEL_NAME"]
        
        print("✅ Graphiti客户端初始化成功")
        
        # 测试添加记忆
        print("📝 添加测试记忆...")
        await client.add_episode(
            name="直接测试记忆",
            episode_body="这是一个直接通过Graphiti客户端添加的测试记忆，用于验证存储功能是否正常。",
            source=EpisodeType.text,
            source_description="直接测试",
            group_id="test_group"
        )
        print("✅ 记忆添加成功")
        
        # 等待处理完成
        print("⏳ 等待处理完成...")
        await asyncio.sleep(5)
        
        # 测试搜索功能
        print("🔍 测试搜索功能...")
        results = await client.search(
            query="测试记忆",
            group_ids=["test_group"],
            num_results=5
        )
        
        print(f"🎯 搜索结果数量: {len(results)}")
        for i, result in enumerate(results):
            print(f"  {i+1}. {result.name}: {result.fact}")
        
        # 检查数据库中的节点数量
        print("📊 检查数据库状态...")
        async with client.driver.session() as session:
            result = await session.run("MATCH (n) RETURN count(n) as node_count")
            record = await result.single()
            node_count = record["node_count"]
            print(f"📈 数据库中的节点总数: {node_count}")
            
            # 检查Episode节点
            result = await session.run("MATCH (n:Episodic) RETURN count(n) as episode_count")
            record = await result.single()
            episode_count = record["episode_count"]
            print(f"📚 Episode节点数量: {episode_count}")
            
            # 检查Entity节点
            result = await session.run("MATCH (n:Entity) RETURN count(n) as entity_count")
            record = await result.single()
            entity_count = record["entity_count"]
            print(f"🏷️ Entity节点数量: {entity_count}")
        
        await client.close()
        print("✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_graphiti())
